import pandas as pd

# Load Excel file
file_path = "JE_template.xlsx"
xls = pd.ExcelFile(file_path)

# Load and clean GL Codes
gl_codes = pd.read_excel(xls, sheet_name='GL codes')

gl_codes_clean = gl_codes.iloc[:, [0, 1, 2, 3, 4, 5]]

gl_codes_clean.columns = ['Product', 'Store', 'Dept', 'GL', 'Sign', 'Type']


gl_codes_clean = gl_codes_clean.dropna(subset=['Product'])


#gl_codes_clean['Type'] = gl_codes_clean['Type'].map({0: 'FA:', 1: 'FD:'})

#Override type by Balasundaram
gl_codes_clean['Type'] = gl_codes_clean['Type'].map({0: 'FA:', 1: 'FA:'})

# Load weekly sales data
weekly_sales = pd.read_excel(xls, sheet_name='weekly sales data- from client', skiprows=5)
weekly_sales = weekly_sales.rename(columns={'Unnamed: 0': 'Product'})
weekly_sales = weekly_sales.dropna(subset=['Product'])

# Constants
COMPANY = 551
BUSINESS_UNIT = 551
STORE = 3337
days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']

# FD codes
fd_codes = ['CA', 'CC', 'GP', 'DC', 'OT']
fd_index = 0

# Final output list
final_rows = []

for day in days:
    # Add day header
    final_rows.append([f'# {day}', '', '', '', '', '', '', '', ''])

    for _, row in gl_codes_clean.iterrows():
        product = str(row['Product']).strip()
        dept = row['Dept']
        gl = row['GL']
        sign = row['Sign']
        type_entry = row['Type']

        # Find matching product in weekly sales
        match = weekly_sales[weekly_sales['Product'].str.strip() == product]
        if not match.empty and day in match.columns:
            amount = match.iloc[0][day]
            if pd.notna(amount):
                #signed_amount = float(amount) * float(sign)
                signed_amount = round(float(amount) * float(sign), 2)

                # ✅ CUSTOMER COUNT → Put in FD_Amount only, no FD row
                if type_entry == 'FA:' and product.upper() == 'CUSTOMER COUNT':
                    final_rows.append([
                        type_entry, COMPANY, BUSINESS_UNIT, STORE, dept, gl,
                        '', signed_amount, ''  # FD_Amount holds the value
                    ])
                else:
                    # All other rows → Signed_Amount column
                    final_rows.append([
                        type_entry, COMPANY, BUSINESS_UNIT, STORE, dept, gl,
                        signed_amount, '', ''
                    ])

                # ✅ Add FD row if GL=1104, FA:, and NOT CUSTOMER COUNT
                if type_entry == 'FA:' and gl == 1104:
                    fd_code = fd_codes[fd_index % len(fd_codes)]
                    fd_index += 1

                    final_rows.append([
                        'FD:', COMPANY, BUSINESS_UNIT, STORE, dept, 1102,
                        '', signed_amount, fd_code
                    ])

    # Two empty rows after each day
    final_rows.append([''] * 9)
    final_rows.append([''] * 9)

# Create DataFrame
output_df = pd.DataFrame(final_rows, columns=[
    'Type', 'Company', 'Business_Unit', 'Location', 'Dept', 'GL',
    'Signed_Amount', 'FD_Amount', 'Code'
])

# Save output
output_df.to_excel("final_output_customer_count_fd_amount.xlsx", index=False)

print("✅ Output saved to 'final_output_customer_count_fd_amount.xlsx'")
